// pdfToTextConverter.js - PDF to Text Converter Module

import * as dom from './domElements.js';

// --- Constants ---
const AZURE_OCR_MODEL_ID = "prebuilt-read";
const API_RETRY_WAIT_SECONDS = 30;
const MAX_RETRIES = 3;
const IMAGE_FORMAT = "PNG";

// --- Module State ---
let currentPdfFile = null;
let pdfDocument = null;
let azureClient = null;
let conversionResults = [];
let isProcessing = false;

// --- Configuration ---
let config = {
    azureEndpoint: '',
    azureKey: '',
    imageDpi: 300,
    saveImages: true
};

// --- Utility Functions ---

/**
 * Updates the status bar with a message and type
 */
function updateStatus(message, type = "info") {
    if (dom.pdfToTextStatusBar) {
        dom.pdfToTextStatusBar.textContent = message;
        dom.pdfToTextStatusBar.className = `status-bar ${type}`;
    }
}

/**
 * Adds a log entry to the conversion log
 */
function addLogEntry(message, type = "info") {
    if (!dom.conversionLog) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
    
    dom.conversionLog.appendChild(logEntry);
    dom.conversionLog.scrollTop = dom.conversionLog.scrollHeight;
}

/**
 * Updates the progress bar
 */
function updateProgress(percentage, text) {
    if (dom.progressBar) {
        dom.progressBar.style.setProperty('--progress-width', `${percentage}%`);
        dom.progressBar.classList.add('active');
    }
    if (dom.progressText) {
        dom.progressText.textContent = text;
    }
}

/**
 * Cleans extracted text by removing common artifacts
 */
function cleanExtractedText(text) {
    if (!text) return "";
    text = text.trim();
    text = text.replace(/\f/g, '\n'); // Replace form feeds with newlines
    text = text.replace(/ +/g, ' '); // Replace multiple spaces with single space
    return text;
}

/**
 * Formats text into paragraphs
 */
function formatTextToParagraphs(text) {
    if (!text) return [];
    text = text.replace(/\r\n/g, "\n").replace(/\r/g, "\n");
    const paragraphs = text.split(/\n\s*\n+/);
    return paragraphs.map(p => p.trim()).filter(p => p.length > 0);
}

// --- Azure AI Document Intelligence Functions ---

/**
 * Tests the Azure connection
 */
async function testAzureConnection() {
    if (!config.azureEndpoint || !config.azureKey) {
        addLogEntry("Azure endpoint and key are required", "error");
        return false;
    }

    try {
        addLogEntry("Testing Azure connection...", "info");
        
        // Create a simple test request
        const response = await fetch(`${config.azureEndpoint}/documentintelligence/documentModels?api-version=2023-07-31`, {
            method: 'GET',
            headers: {
                'Ocp-Apim-Subscription-Key': config.azureKey,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            addLogEntry("Azure connection successful", "success");
            return true;
        } else {
            addLogEntry(`Azure connection failed: ${response.status} ${response.statusText}`, "error");
            return false;
        }
    } catch (error) {
        addLogEntry(`Azure connection error: ${error.message}`, "error");
        return false;
    }
}

/**
 * Extracts text from an image using Azure AI Document Intelligence
 */
async function extractTextWithAzureDI(imageBlob, pageNumber) {
    addLogEntry(`Analyzing page ${pageNumber} with Azure DI...`, "info");

    let retries = 0;
    let lastError = null;

    while (retries < MAX_RETRIES) {
        try {
            // Start the analysis
            const analyzeResponse = await fetch(`${config.azureEndpoint}/documentintelligence/documentModels/${AZURE_OCR_MODEL_ID}:analyze?api-version=2023-07-31`, {
                method: 'POST',
                headers: {
                    'Ocp-Apim-Subscription-Key': config.azureKey,
                    'Content-Type': 'application/octet-stream'
                },
                body: imageBlob
            });

            if (!analyzeResponse.ok) {
                throw new Error(`HTTP ${analyzeResponse.status}: ${analyzeResponse.statusText}`);
            }

            const operationLocation = analyzeResponse.headers.get('Operation-Location');
            if (!operationLocation) {
                throw new Error('No operation location returned from Azure');
            }

            // Poll for results
            let result = null;
            let pollAttempts = 0;
            const maxPollAttempts = 60; // 5 minutes max

            while (pollAttempts < maxPollAttempts) {
                await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
                
                const resultResponse = await fetch(operationLocation, {
                    headers: {
                        'Ocp-Apim-Subscription-Key': config.azureKey
                    }
                });

                if (!resultResponse.ok) {
                    throw new Error(`Polling failed: ${resultResponse.status}`);
                }

                const resultData = await resultResponse.json();
                
                if (resultData.status === 'succeeded') {
                    result = resultData.analyzeResult;
                    break;
                } else if (resultData.status === 'failed') {
                    throw new Error('Azure analysis failed');
                } else if (resultData.status === 'running') {
                    addLogEntry(`Page ${pageNumber}: Analysis in progress...`, "info");
                    pollAttempts++;
                } else {
                    throw new Error(`Unexpected status: ${resultData.status}`);
                }
            }

            if (!result) {
                throw new Error('Analysis timed out');
            }

            if (result.content) {
                addLogEntry(`Page ${pageNumber}: Extracted ${result.content.length} characters`, "success");
                return { content: result.content, error: null };
            } else {
                addLogEntry(`Page ${pageNumber}: No text content found`, "warning");
                return { content: "", error: null };
            }

        } catch (error) {
            lastError = error;
            addLogEntry(`Page ${pageNumber}: Error (attempt ${retries + 1}): ${error.message}`, "warning");
            
            if (error.message.includes('429') || error.message.includes('throttle')) {
                retries++;
                if (retries < MAX_RETRIES) {
                    addLogEntry(`Page ${pageNumber}: Rate limited, waiting ${API_RETRY_WAIT_SECONDS}s...`, "info");
                    await new Promise(resolve => setTimeout(resolve, API_RETRY_WAIT_SECONDS * 1000));
                    continue;
                }
            } else if (error.message.includes('401') || error.message.includes('403')) {
                return { content: null, error: "Authentication failed. Check Azure credentials." };
            } else {
                retries++;
                if (retries < MAX_RETRIES) {
                    addLogEntry(`Page ${pageNumber}: Retrying in ${API_RETRY_WAIT_SECONDS}s...`, "info");
                    await new Promise(resolve => setTimeout(resolve, API_RETRY_WAIT_SECONDS * 1000));
                    continue;
                }
            }
        }
    }

    return { content: null, error: `Failed after ${MAX_RETRIES} retries: ${lastError?.message}` };
}

// --- PDF Processing Functions ---

/**
 * Renders a PDF page to canvas and returns image blob
 */
async function renderPageToBlob(page, dpi = 300) {
    const viewport = page.getViewport({ scale: dpi / 72 });
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    const renderContext = {
        canvasContext: context,
        viewport: viewport
    };

    await page.render(renderContext).promise;
    
    return new Promise(resolve => {
        canvas.toBlob(resolve, `image/${IMAGE_FORMAT.toLowerCase()}`, 0.95);
    });
}

/**
 * Loads and displays PDF preview
 */
async function loadPdfPreview(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
        pdfDocument = await loadingTask.promise;
        
        addLogEntry(`PDF loaded: ${pdfDocument.numPages} pages`, "success");
        
        // Render first page for preview
        const page = await pdfDocument.getPage(1);
        const viewport = page.getViewport({ scale: 1.0 });
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };

        await page.render(renderContext).promise;
        
        // Display in preview container
        dom.pdfPreviewContainer.innerHTML = '';
        dom.pdfPreviewContainer.appendChild(canvas);
        
        // Update file details
        dom.pdfFileDetails.innerHTML = `
            <strong>File:</strong> ${file.name}<br>
            <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
            <strong>Pages:</strong> ${pdfDocument.numPages}
        `;
        
        updateButtonStates();
        
    } catch (error) {
        addLogEntry(`Error loading PDF: ${error.message}`, "error");
        updateStatus(`Failed to load PDF: ${error.message}`, "error");
    }
}

/**
 * Updates button states based on current state
 */
function updateButtonStates() {
    const hasConfig = config.azureEndpoint && config.azureKey;
    const hasPdf = currentPdfFile && pdfDocument;
    
    if (dom.testAzureConnectionBtn) {
        dom.testAzureConnectionBtn.disabled = !hasConfig || isProcessing;
    }
    
    if (dom.startConversionBtn) {
        dom.startConversionBtn.disabled = !hasConfig || !hasPdf || isProcessing;
    }
    
    if (dom.downloadResultBtn) {
        dom.downloadResultBtn.disabled = conversionResults.length === 0 || isProcessing;
    }
}

// --- Event Handlers ---

/**
 * Handles file input change
 */
async function handleFileInput(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.pdf')) {
        updateStatus("Please select a PDF file.", "error");
        return;
    }
    
    currentPdfFile = file;
    dom.pdfToTextFileInfo.textContent = `PDF loaded: ${file.name}`;
    
    await loadPdfPreview(file);
}

/**
 * Handles configuration changes
 */
function handleConfigChange() {
    config.azureEndpoint = dom.azureDiEndpoint?.value?.trim() || '';
    config.azureKey = dom.azureDiKey?.value?.trim() || '';
    config.imageDpi = parseInt(dom.imageDpiSelect?.value) || 300;
    config.saveImages = dom.saveImagesCheckbox?.checked || false;
    
    updateButtonStates();
}

/**
 * Handles Azure connection test
 */
async function handleTestConnection() {
    if (isProcessing) return;

    isProcessing = true;
    updateButtonStates();

    const success = await testAzureConnection();

    // Add visual indicator
    const indicator = document.createElement('span');
    indicator.className = `connection-status ${success ? 'connected' : 'disconnected'}`;

    const existingIndicator = dom.testAzureConnectionBtn.querySelector('.connection-status');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    dom.testAzureConnectionBtn.appendChild(indicator);

    isProcessing = false;
    updateButtonStates();
}

/**
 * Handles the main conversion process
 */
async function handleStartConversion() {
    if (isProcessing || !currentPdfFile || !pdfDocument) return;

    isProcessing = true;
    updateButtonStates();
    conversionResults = [];

    addLogEntry("Starting PDF to text conversion...", "info");
    updateStatus("Converting PDF to text...", "info");

    const totalPages = pdfDocument.numPages;
    const startTime = Date.now();

    try {
        // Create Word document content
        let wordContent = `
            <html>
                <head>
                    <meta charset="utf-8">
                    <title>Extracted Text from ${currentPdfFile.name}</title>
                </head>
                <body>
                    <h1>Extracted Text from: ${currentPdfFile.name}</h1>
                    <p><strong>Source PDF:</strong> ${currentPdfFile.name}</p>
                    <p><strong>Processing Date:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Azure DI Model:</strong> ${AZURE_OCR_MODEL_ID}</p>
                    <p><strong>Total Pages:</strong> ${totalPages}</p>
                    <hr>
        `;

        for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
            updateProgress((pageNum - 1) / totalPages * 100, `Processing page ${pageNum} of ${totalPages}...`);
            addLogEntry(`Processing page ${pageNum}/${totalPages}...`, "info");

            try {
                const page = await pdfDocument.getPage(pageNum);
                const imageBlob = await renderPageToBlob(page, config.imageDpi);

                if (!imageBlob) {
                    addLogEntry(`Page ${pageNum}: Failed to render page`, "error");
                    wordContent += `<p><strong>Page ${pageNum}:</strong> [ERROR: Failed to render page]</p>`;
                    continue;
                }

                const result = await extractTextWithAzureDI(imageBlob, pageNum);

                if (result.error) {
                    addLogEntry(`Page ${pageNum}: ${result.error}`, "error");
                    wordContent += `<p><strong>Page ${pageNum}:</strong> [ERROR: ${result.error}]</p>`;
                } else if (result.content) {
                    const cleanedText = cleanExtractedText(result.content);
                    const paragraphs = formatTextToParagraphs(cleanedText);

                    wordContent += `<h2>Page ${pageNum}</h2>`;

                    if (paragraphs.length > 0) {
                        paragraphs.forEach(paragraph => {
                            wordContent += `<p>${paragraph.replace(/\n/g, '<br>')}</p>`;
                        });
                    } else {
                        wordContent += `<p>[No text content found on this page]</p>`;
                    }

                    conversionResults.push({
                        pageNumber: pageNum,
                        content: cleanedText,
                        paragraphs: paragraphs
                    });
                } else {
                    addLogEntry(`Page ${pageNum}: No text content found`, "warning");
                    wordContent += `<p><strong>Page ${pageNum}:</strong> [No text content found]</p>`;
                }

            } catch (pageError) {
                addLogEntry(`Page ${pageNum}: Unexpected error - ${pageError.message}`, "error");
                wordContent += `<p><strong>Page ${pageNum}:</strong> [ERROR: ${pageError.message}]</p>`;
            }
        }

        wordContent += `
                    <hr>
                    <p><em>Processing completed in ${((Date.now() - startTime) / 1000).toFixed(2)} seconds</em></p>
                </body>
            </html>
        `;

        // Store the Word document content for download
        window.pdfConversionResult = wordContent;

        updateProgress(100, "Conversion completed!");
        addLogEntry(`Conversion completed successfully. Processed ${totalPages} pages in ${((Date.now() - startTime) / 1000).toFixed(2)} seconds.`, "success");
        updateStatus("Conversion completed. Ready to download.", "success");

    } catch (error) {
        addLogEntry(`Conversion failed: ${error.message}`, "error");
        updateStatus(`Conversion failed: ${error.message}`, "error");
    } finally {
        isProcessing = false;
        updateButtonStates();
    }
}

/**
 * Handles downloading the conversion result
 */
function handleDownloadResult() {
    if (!window.pdfConversionResult) {
        updateStatus("No conversion result available.", "error");
        return;
    }

    try {
        // Convert HTML to DOCX using html-docx-js
        const docxBlob = htmlDocx.asBlob(window.pdfConversionResult);

        // Create download link
        const url = URL.createObjectURL(docxBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentPdfFile.name.replace('.pdf', '_extracted_text.docx');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        addLogEntry("Word document downloaded successfully", "success");
        updateStatus("Document downloaded successfully.", "success");

    } catch (error) {
        addLogEntry(`Download failed: ${error.message}`, "error");
        updateStatus(`Download failed: ${error.message}`, "error");
    }
}

// --- Initialization ---

/**
 * Initializes the PDF to Text Converter module
 */
export function initPdfToTextConverter() {
    console.log("Initializing PDF to Text Converter...");
    
    // Load configuration from environment variables
    config.azureEndpoint = import.meta.env?.VITE_AZURE_DI_ENDPOINT || '';
    config.azureKey = import.meta.env?.VITE_AZURE_DI_KEY || '';
    
    // Set initial values
    if (dom.azureDiEndpoint && config.azureEndpoint) {
        dom.azureDiEndpoint.value = config.azureEndpoint;
    }
    if (dom.azureDiKey && config.azureKey) {
        dom.azureDiKey.value = config.azureKey;
    }
    
    // Set up event listeners
    if (dom.pdfFileInput) {
        dom.pdfFileInput.addEventListener('change', handleFileInput);
    }
    
    if (dom.azureDiEndpoint) {
        dom.azureDiEndpoint.addEventListener('input', handleConfigChange);
    }
    
    if (dom.azureDiKey) {
        dom.azureDiKey.addEventListener('input', handleConfigChange);
    }
    
    if (dom.imageDpiSelect) {
        dom.imageDpiSelect.addEventListener('change', handleConfigChange);
    }
    
    if (dom.saveImagesCheckbox) {
        dom.saveImagesCheckbox.addEventListener('change', handleConfigChange);
    }
    
    if (dom.testAzureConnectionBtn) {
        dom.testAzureConnectionBtn.addEventListener('click', handleTestConnection);
    }

    if (dom.startConversionBtn) {
        dom.startConversionBtn.addEventListener('click', handleStartConversion);
    }

    if (dom.downloadResultBtn) {
        dom.downloadResultBtn.addEventListener('click', handleDownloadResult);
    }
    
    // Initialize PDF.js worker
    if (typeof pdfjsLib !== 'undefined') {
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    }
    
    // Initialize UI state
    handleConfigChange();
    updateButtonStates();
    updateStatus("PDF to Text Converter Ready.", "info");
    addLogEntry("PDF to Text Converter initialized", "info");
    
    console.log("PDF to Text Converter initialized successfully.");
}
