// index.js - Main Application Entry Point

// --- IMPORTS ---
import * as dom from './domElements.js';
import * as ui from './ui.js';
import * as state from './state.js';
import * as audioService from './audioService.js';
import * as fileService from './fileService.js';
import * as documentService from './documentService.js';
import * as ttsService from './ttsService.js';
import { initAiVerificationEventListeners } from './aiVerification.js';
import { initAiVoiceCreator } from './aiVoiceCreator.js';
import { initSsmEditorEventListeners } from './ssmlEditor.js';
import { initWhisperService } from './whisperService.js';
import { initQuoteFootnoteInserter } from './quoteFootnoteInserter.js';
import * as themeService from './themeService.js';
import { DEFAULT_G_TTS_VOICE_NAME, DEFAULT_MS_TTS_VOICE_NAME } from './constants.js';

// --- MAIN INITIALIZATION ---

/**
 * The primary function to start the application.
 */
async function initializeApp() {
    console.log("Starting application initialization...");

    // 1. Find all DOM elements
    dom.initDOMelements();

    // 2. Initialize themes
    await themeService.initThemes();
    ui.populateThemeSelector(themeService.getAvailableThemes(), 'default');
    themeService.applyTheme('default');

    // 3. Set up navigation and set the initial view
    dom.initNavigationEventListeners(); // This correctly centralizes navigation logic

    // 4. Connect modules by registering callbacks
    ui.registerUICallbacks({
        selectMusicTrack: audioService.selectMusicTrack,
        selectDocument: documentService.selectDocument,
        insertBreakTag: documentService.insertBreakTag,
    });

    // 5. Initialize all other event listeners
    initEventListeners();

    // 6. Populate UI elements that require async data
    fetchAndPopulateVoices();
    ui.renderBreakTagButtons();

    // 7. Set initial UI state
    ui.updateAudioControlsUI();



    console.log("Application Initialized Successfully.");
}

/**
 * A helper function to set up all event listeners in one place.
 */
function initEventListeners() {
    // Audio Player
    dom.audioPlayer.addEventListener('loadedmetadata', audioService.handleAudioLoadedMetadata);
    dom.audioPlayer.addEventListener('timeupdate', audioService.handleAudioTimeUpdate);
    dom.audioPlayer.addEventListener('ended', audioService.handleAudioEnded);
    dom.audioPlayer.addEventListener('play', () => {
        state.setIsPlaying(true);
        ui.updatePlayPauseButton();
    });
    dom.audioPlayer.addEventListener('pause', () => {
        state.setIsPlaying(false);
        ui.updatePlayPauseButton();
    });
    
    // Audio Controls
    dom.playPauseBtn.addEventListener('click', audioService.togglePlayPause);
    dom.nextTrackBtn.addEventListener('click', audioService.playNextTrack);
    dom.prevTrackBtn.addEventListener('click', audioService.playPrevTrack);
    dom.progressBar.addEventListener('input', audioService.handleSeek);
    dom.volumeSlider.addEventListener('input', audioService.handleVolumeChange);
    
    // File Inputs
    dom.musicFolderInput.addEventListener('change', (e) => fileService.handleFolderSelection(e, 'music'));
    dom.docFolderInput.addEventListener('change', (e) => fileService.handleFolderSelection(e, 'docs'));
    
    // Document Editor
    dom.toggleEditBtn.addEventListener('click', documentService.toggleEditMode);
    dom.saveDocBtn.addEventListener('click', documentService.saveDocument);
    dom.saveToFolderBtn.addEventListener('click', documentService.saveToOriginalFolder);
    dom.debugDocxBtn.addEventListener('click', documentService.debugCurrentDocx);

    // TTS Voice Selection
    dom.gTtsVoiceSelect.addEventListener('change', (e) => state.setSelectedGTTSVoiceName(e.target.value));
    dom.msTtsVoiceSelect.addEventListener('change', (e) => state.setSelectedMsTTSVoiceName(e.target.value));

    // TTS Reprocessing Buttons
    dom.reprocessGTTSBtn.addEventListener('click', ttsService.handleReprocessWithGoogleTTS);
    dom.reprocessMsTTSBtn.addEventListener('click', ttsService.handleReprocessWithMicrosoftTTS);

    // Theme Selection
    if (dom.themeSelect) {
        dom.themeSelect.addEventListener('change', (e) => {
            const themeName = e.target.value;
            themeService.applyTheme(themeName);
        });
    }

    // Initialize feature-specific listeners from other modules
    const uiHelpers = {
        updateStatus: ui.updateStatus,
        showSSMLModal: ui.showSSMLModal,
        hideSSMLModal: ui.hideSSMLModal,
        updateSSMLStatusBar: ui.updateSSMLStatusBar,
        populateGoogleVoiceSelector: ui.populateGoogleVoiceSelector,
        populateMicrosoftVoiceSelector: ui.populateMicrosoftVoiceSelector,
    };
    initAiVerificationEventListeners(uiHelpers);
    initAiVoiceCreator(uiHelpers);
    initSsmEditorEventListeners(uiHelpers);
    initWhisperService(uiHelpers);
    initQuoteFootnoteInserter();
}

/**
 * Fetches TTS voice lists from services and populates the dropdowns.
 */
async function fetchAndPopulateVoices() {
    // Google Voices
    state.setAreGVoicesLoading(true);
    ui.populateGoogleVoiceSelector(dom.gTtsVoiceSelect, [], null);

    const gVoices = await ttsService.fetchAvailableGoogleVoices();
    state.setFetchedGTTSVoices(gVoices);
    state.setAreGVoicesLoading(false);
    ui.populateGoogleVoiceSelector(dom.gTtsVoiceSelect, gVoices, DEFAULT_G_TTS_VOICE_NAME);

    // Microsoft Voices
    state.setAreMsVoicesLoading(true);
    ui.populateMicrosoftVoiceSelector(dom.msTtsVoiceSelect, [], null);

    const msVoices = await ttsService.fetchAvailableMicrosoftVoices();
    state.setFetchedMsTTSVoices(msVoices);
    state.setAreMsVoicesLoading(false);
    ui.populateMicrosoftVoiceSelector(dom.msTtsVoiceSelect, msVoices, DEFAULT_MS_TTS_VOICE_NAME);

    // Populate AI Voice Creator dropdown based on currently selected provider
    if (dom.aiVoiceTTSVoiceSelect && dom.aiVoiceTTSProviderSelect) {
        const currentProvider = dom.aiVoiceTTSProviderSelect.value;
        if (currentProvider === 'google') {
            ui.populateGoogleVoiceSelector(dom.aiVoiceTTSVoiceSelect, gVoices, DEFAULT_G_TTS_VOICE_NAME);
        } else if (currentProvider === 'microsoft') {
            ui.populateMicrosoftVoiceSelector(dom.aiVoiceTTSVoiceSelect, msVoices, DEFAULT_MS_TTS_VOICE_NAME);
        }
    }

    ui.updateAudioControlsUI();
}

// --- START THE APPLICATION ---
document.addEventListener('DOMContentLoaded', initializeApp);