{"name": "web-media-player-doc-editor", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "concurrently \"vite\" \"python whisper_server.py\"", "dev:vite-only": "vite", "build": "tsc && vite build", "preview": "vite preview", "whisper": "python whisper_server.py", "dev:full": "concurrently \"npm run dev:vite-only\" \"npm run whisper\"", "start": "npm run dev"}, "dependencies": {"docx": "^9.5.0", "pizzip": "^3.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "concurrently": "^9.1.2", "typescript": "^5.5.3", "vite": "^5.3.3"}}