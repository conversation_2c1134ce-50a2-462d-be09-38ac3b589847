<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote and Footnote Inserter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-text {
            background: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Quote and Footnote Inserter Test</h1>
        <p>This page tests the Quote and Footnote Inserter functionality independently.</p>
        
        <div class="test-result info">
            <h3>📋 Test Sample Text</h3>
            <div class="test-text" id="sample-text">This is a sample text with "quoted content" and footnote some important note end footnote for testing purposes. Here's another "quote to test" and footnote another note end footnote.</div>
        </div>
        
        <div class="test-result info">
            <h3>🔧 Test Controls</h3>
            <button onclick="testRegexPattern()">Test Regex Pattern</button>
            <button onclick="testAnnotationExtraction()">Test Annotation Extraction</button>
            <button onclick="testMarkerInsertion()">Test Marker Insertion</button>
            <button onclick="openMainApp()">Open Main App</button>
        </div>
        
        <div id="test-results" class="test-result info">
            <h3>📊 Test Results</h3>
            <div id="results-content">Click a test button to see results...</div>
        </div>
    </div>

    <script>
        // Test the regex pattern
        function testRegexPattern() {
            const ANNOTATION_PATTERN = /[""]([^""]*?)[""]|footnote\s+(.*?)\s+end footnote/gi;
            const testText = document.getElementById('sample-text').textContent;
            
            let results = [];
            let match;
            ANNOTATION_PATTERN.lastIndex = 0;
            
            while ((match = ANNOTATION_PATTERN.exec(testText)) !== null) {
                results.push({
                    fullMatch: match[0],
                    quote: match[1],
                    footnote: match[2],
                    index: match.index
                });
            }
            
            displayResults('Regex Pattern Test', results);
        }
        
        // Test annotation extraction
        function testAnnotationExtraction() {
            const testText = document.getElementById('sample-text').textContent;
            const annotations = getAnnotationsFromText(testText);
            displayResults('Annotation Extraction Test', annotations);
        }
        
        // Test marker insertion
        function testMarkerInsertion() {
            const testText = 'This has "quoted text" in it.';
            const result = testText.replace(/[""]([^""]*?)[""]/, 'quote, "$1" end quote,');
            displayResults('Marker Insertion Test', { original: testText, modified: result });
        }
        
        function getAnnotationsFromText(text) {
            const ANNOTATION_PATTERN = /[""]([^""]*?)[""]|footnote\s+(.*?)\s+end footnote/gi;
            const MARKER_TEXTS = new Set(["quote", "end quote", "footnote", "end footnote"]);
            
            const foundAnnotations = [];
            let match;
            
            ANNOTATION_PATTERN.lastIndex = 0;
            
            while ((match = ANNOTATION_PATTERN.exec(text)) !== null) {
                let annotationType, annotationText, startPos, endPos;
                
                if (match[1] !== undefined) {
                    annotationType = "Quote";
                    annotationText = match[1];
                    startPos = match.index + match[0].indexOf(match[1]);
                    endPos = startPos + match[1].length;
                } else if (match[2] !== undefined) {
                    annotationType = "Footnote";
                    annotationText = match[2];
                    startPos = match.index + match[0].indexOf(match[2]);
                    endPos = startPos + match[2].length;
                }
                
                if (annotationText && !MARKER_TEXTS.has(annotationText.toLowerCase().trim())) {
                    foundAnnotations.push({
                        type: annotationType,
                        text: annotationText,
                        start: startPos,
                        end: endPos,
                        fullStart: match.index,
                        fullEnd: match.index + match[0].length
                    });
                }
            }
            
            foundAnnotations.sort((a, b) => a.start - b.start);
            return foundAnnotations;
        }
        
        function displayResults(testName, results) {
            const resultsDiv = document.getElementById('results-content');
            resultsDiv.innerHTML = `
                <h4>${testName}</h4>
                <pre>${JSON.stringify(results, null, 2)}</pre>
            `;
        }
        
        function openMainApp() {
            window.open('http://localhost:8000', '_blank');
        }
    </script>
</body>
</html>
