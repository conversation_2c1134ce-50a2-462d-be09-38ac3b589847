// quoteFootnoteInserter.js - Quote and Footnote Inserter Module

import * as dom from './domElements.js';

// --- Constants ---
const BG_COLOR = "#192537";
const FRAME_BG_COLOR = "#232F40";
const ACCENT_COLOR = "#B09E80";
const TEXT_COLOR = "#FFFFFF";
const FONT_FAMILY = "Segoe UI";

// Consolidated Regex Pattern for finding all annotations
const ANNOTATION_PATTERN = /[""]([^""]*?)[""]|footnote\s+(.*?)\s+end footnote/gi;
const MARKER_TEXTS = new Set(["quote", "end quote", "footnote", "end footnote"]);

// --- Module State ---
let currentDocument = null;
let annotations = [];
let selectedAnnotationIndex = null;

// --- Core Functions ---

/**
 * Extracts all quotes and footnotes using a single combined pattern
 */
function getAnnotationsFromText(text) {
    const foundAnnotations = [];
    let match;
    
    // Reset regex lastIndex to ensure we start from the beginning
    ANNOTATION_PATTERN.lastIndex = 0;
    
    while ((match = ANNOTATION_PATTERN.exec(text)) !== null) {
        let annotationType, annotationText, startPos, endPos;
        
        if (match[1] !== undefined) {
            // Quote match
            annotationType = "Quote";
            annotationText = match[1];
            startPos = match.index + match[0].indexOf(match[1]);
            endPos = startPos + match[1].length;
        } else if (match[2] !== undefined) {
            // Footnote match
            annotationType = "Footnote";
            annotationText = match[2];
            startPos = match.index + match[0].indexOf(match[2]);
            endPos = startPos + match[2].length;
        }
        
        // Only add if the text is not a marker text
        if (annotationText && !MARKER_TEXTS.has(annotationText.toLowerCase().trim())) {
            foundAnnotations.push({
                type: annotationType,
                text: annotationText,
                start: startPos,
                end: endPos,
                fullStart: match.index,
                fullEnd: match.index + match[0].length
            });
        }
    }
    
    // Sort by start position
    foundAnnotations.sort((a, b) => a.start - b.start);
    return foundAnnotations;
}

/**
 * Updates the UI with current annotations
 */
function updateUIWithAnnotations() {
    selectedAnnotationIndex = null;
    const textContent = dom.quoteFootnoteTextArea.value;
    annotations = getAnnotationsFromText(textContent);
    applyTextAreaFormatting();
    populateAnnotationsList();
    updateButtonStates();
}

/**
 * Applies formatting to the text area (placeholder for highlighting)
 */
function applyTextAreaFormatting() {
    // Note: HTML textarea doesn't support rich text formatting
    // This is a placeholder for potential future enhancement
    // We could implement highlighting using overlays or convert to contenteditable
}

/**
 * Populates the annotations list in the UI
 */
function populateAnnotationsList() {
    const listContainer = dom.quoteFootnoteAnnotationsList;
    listContainer.innerHTML = '';
    
    if (annotations.length === 0) {
        listContainer.innerHTML = '<p style="color: var(--text-color); opacity: 0.7; text-align: center; padding: 20px;">No quotes or footnotes found.</p>';
        return;
    }
    
    annotations.forEach((annotation, index) => {
        const annotationItem = document.createElement('div');
        annotationItem.className = 'annotation-item';
        annotationItem.dataset.index = index;
        
        const typeElement = document.createElement('div');
        typeElement.className = 'annotation-type';
        typeElement.textContent = annotation.type;
        
        const textElement = document.createElement('div');
        textElement.className = 'annotation-text';
        textElement.textContent = annotation.text;
        
        annotationItem.appendChild(typeElement);
        annotationItem.appendChild(textElement);
        
        // Add click event listener
        annotationItem.addEventListener('click', () => selectAnnotation(index));
        
        listContainer.appendChild(annotationItem);
    });
}

/**
 * Selects an annotation and highlights it
 */
function selectAnnotation(index) {
    // Remove previous selection
    const previousSelected = dom.quoteFootnoteAnnotationsList.querySelector('.annotation-item.selected');
    if (previousSelected) {
        previousSelected.classList.remove('selected');
    }
    
    // Add selection to new item
    const selectedItem = dom.quoteFootnoteAnnotationsList.querySelector(`[data-index="${index}"]`);
    if (selectedItem) {
        selectedItem.classList.add('selected');
        selectedAnnotationIndex = index;
        
        // Highlight corresponding text in textarea
        const annotation = annotations[index];
        dom.quoteFootnoteTextArea.focus();
        dom.quoteFootnoteTextArea.setSelectionRange(annotation.start, annotation.end);
        
        updateButtonStates();
    }
}

/**
 * Updates button states based on current selection and document state
 */
function updateButtonStates() {
    const hasDocument = currentDocument !== null;
    const hasSelection = selectedAnnotationIndex !== null;
    
    dom.quoteFootnoteSaveBtn.disabled = !hasDocument;
    dom.quoteFootnoteAddQuoteMarkersBtn.disabled = !hasDocument || !hasSelection;
    dom.quoteFootnoteAddFootnoteMarkersBtn.disabled = !hasDocument || !hasSelection;
}

/**
 * Adds markers around selected annotation
 */
function addMarkers(markerType) {
    if (selectedAnnotationIndex === null) {
        updateStatus("Please select an annotation from the list.", "error");
        return;
    }
    
    const annotation = annotations[selectedAnnotationIndex];
    const startMarker = `${markerType}, `;
    const endMarker = ` end ${markerType},`;
    
    const textArea = dom.quoteFootnoteTextArea;
    const currentText = textArea.value;
    
    // Insert markers
    const newText = 
        currentText.substring(0, annotation.fullStart) +
        startMarker +
        currentText.substring(annotation.fullStart, annotation.fullEnd) +
        endMarker +
        currentText.substring(annotation.fullEnd);
    
    textArea.value = newText;
    updateUIWithAnnotations();
    updateStatus(`Added ${markerType} markers successfully.`, "success");
}

/**
 * Updates the status bar
 */
function updateStatus(message, type = "info") {
    dom.quoteFootnoteStatusBar.textContent = message;
    dom.quoteFootnoteStatusBar.className = `status-bar ${type}`;
}

// --- Event Handlers ---

/**
 * Handles file input change
 */
async function handleFileInput(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.docx')) {
        updateStatus("Please select a .docx file.", "error");
        return;
    }
    
    try {
        updateStatus("Loading document...", "info");
        
        // Use mammoth.js to extract text from DOCX
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.extractRawText({ arrayBuffer });
        
        if (result.value) {
            currentDocument = {
                file: file,
                originalText: result.value,
                name: file.name
            };
            
            dom.quoteFootnoteTextArea.value = result.value;
            dom.quoteFootnoteFileInfo.textContent = `Document loaded: ${file.name}`;
            
            updateUIWithAnnotations();
            updateStatus(`Document "${file.name}" loaded successfully.`, "success");
        } else {
            throw new Error("No text content found in document");
        }
    } catch (error) {
        console.error("Error loading document:", error);
        updateStatus(`Failed to load document: ${error.message}`, "error");
        currentDocument = null;
        updateButtonStates();
    }
}

/**
 * Handles save functionality
 */
async function handleSave() {
    if (!currentDocument) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Saving document...", "info");
        
        // Create new document with modified text
        const modifiedText = dom.quoteFootnoteTextArea.value;
        
        // Use html-docx-js to create a new DOCX file
        const htmlContent = `
            <html>
                <body>
                    ${modifiedText.split('\n').map(para => {
                        if (!para.trim()) return '<p></p>';
                        
                        // Apply bold formatting to annotations
                        let formattedPara = para;
                        
                        // Replace quotes with bold formatting
                        formattedPara = formattedPara.replace(/[""]([^""]*?)[""]|footnote\s+(.*?)\s+end footnote/gi, (match, quote, footnote) => {
                            if (quote !== undefined) {
                                return `"<strong>${quote}</strong>"`;
                            } else if (footnote !== undefined) {
                                return `footnote <strong>${footnote}</strong> end footnote`;
                            }
                            return match;
                        });
                        
                        return `<p>${formattedPara}</p>`;
                    }).join('')}
                </body>
            </html>
        `;
        
        // Convert HTML to DOCX
        const docxBlob = htmlDocx.asBlob(htmlContent);
        
        // Create download link
        const url = URL.createObjectURL(docxBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentDocument.name.replace('.docx', '_annotated.docx');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        updateStatus("Document saved successfully.", "success");
    } catch (error) {
        console.error("Error saving document:", error);
        updateStatus(`Failed to save document: ${error.message}`, "error");
    }
}

// --- Initialization ---

/**
 * Initializes the Quote and Footnote Inserter module
 */
export function initQuoteFootnoteInserter() {
    console.log("Initializing Quote and Footnote Inserter...");
    
    // Set up event listeners
    if (dom.quoteFootnoteFileInput) {
        dom.quoteFootnoteFileInput.addEventListener('change', handleFileInput);
    }
    
    if (dom.quoteFootnoteSaveBtn) {
        dom.quoteFootnoteSaveBtn.addEventListener('click', handleSave);
    }
    
    if (dom.quoteFootnoteAddQuoteMarkersBtn) {
        dom.quoteFootnoteAddQuoteMarkersBtn.addEventListener('click', () => addMarkers('quote'));
    }
    
    if (dom.quoteFootnoteAddFootnoteMarkersBtn) {
        dom.quoteFootnoteAddFootnoteMarkersBtn.addEventListener('click', () => addMarkers('footnote'));
    }
    
    if (dom.quoteFootnoteTextArea) {
        dom.quoteFootnoteTextArea.addEventListener('input', updateUIWithAnnotations);
    }
    
    // Initialize UI state
    updateButtonStates();
    updateStatus("Quote and Footnote Inserter Ready.", "info");
    
    console.log("Quote and Footnote Inserter initialized successfully.");
}
