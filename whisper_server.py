#!/usr/bin/env python3
"""
Local Whisper Transcription Server
==================================

A simple HTTP server that provides local Whisper transcription services
for the web media player application.

Requirements:
- pip install openai-whisper flask flask-cors

Usage:
- python whisper_server.py
- Server runs on http://localhost:8001
- Endpoint: POST /transcribe

Installation Guide:
1. Install Python 3.8+ if not already installed
2. Install required packages:
   pip install openai-whisper flask flask-cors
3. Run the server:
   python whisper_server.py
4. The first time you use a model, it will be downloaded automatically
"""

import os
import sys
import tempfile
import logging
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS
import whisper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for web browser requests

# Global model cache to avoid reloading models
model_cache = {}

# Available models with their approximate sizes
AVAILABLE_MODELS = {
    'tiny': {'size': '39 MB', 'description': 'Fastest, lowest quality'},
    'base': {'size': '74 MB', 'description': 'Fast, good quality'},
    'small': {'size': '244 MB', 'description': 'Balanced speed/quality'},
    'medium': {'size': '769 MB', 'description': 'Good quality'},
    'large': {'size': '1550 MB', 'description': 'Best quality'},
    'large-v2': {'size': '1550 MB', 'description': 'Improved large model'},
    'large-v3': {'size': '1550 MB', 'description': 'Latest large model'}
}

def load_whisper_model(model_name):
    """Load a Whisper model, using cache if available."""
    if model_name not in model_cache:
        try:
            logger.info(f"Loading Whisper model: {model_name}")
            model_cache[model_name] = whisper.load_model(model_name)
            logger.info(f"Successfully loaded model: {model_name}")
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {str(e)}")
            raise
    return model_cache[model_name]

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'service': 'whisper-transcription-server',
        'available_models': list(AVAILABLE_MODELS.keys()),
        'loaded_models': list(model_cache.keys())
    })

@app.route('/models', methods=['GET'])
def get_models():
    """Get available Whisper models."""
    return jsonify({
        'models': AVAILABLE_MODELS,
        'loaded_models': list(model_cache.keys())
    })

@app.route('/transcribe', methods=['POST'])
def transcribe_audio():
    """
    Transcribe audio file using local Whisper.
    
    Expected form data:
    - file: Audio file (required)
    - model: Model name (optional, defaults to 'base')
    - language: Language code (optional, auto-detect if not provided)
    """
    try:
        # Validate request
        if 'file' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400
        
        audio_file = request.files['file']
        if audio_file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Get parameters
        model_name = request.form.get('model', 'base')
        language = request.form.get('language', None)
        
        # Validate model
        if model_name not in AVAILABLE_MODELS:
            return jsonify({
                'error': f'Invalid model: {model_name}',
                'available_models': list(AVAILABLE_MODELS.keys())
            }), 400
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(audio_file.filename).suffix) as temp_file:
            audio_file.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            # Load model
            logger.info(f"Transcribing {audio_file.filename} with model {model_name}")
            model = load_whisper_model(model_name)
            
            # Transcribe audio
            transcribe_options = {}
            if language:
                transcribe_options['language'] = language
            
            result = model.transcribe(temp_path, word_timestamps=True, **transcribe_options)

            # Extract transcript text
            transcript = result['text'].strip()

            # Get detected language
            detected_language = result.get('language', 'unknown')

            # Extract segments with word-level timestamps and confidence
            segments = []
            if 'segments' in result:
                for segment in result['segments']:
                    segment_data = {
                        'start': segment.get('start', 0),
                        'end': segment.get('end', 0),
                        'text': segment.get('text', '').strip(),
                        'words': []
                    }

                    # Extract word-level data if available
                    if 'words' in segment:
                        for word in segment['words']:
                            word_data = {
                                'word': word.get('word', '').strip(),
                                'start': word.get('start', 0),
                                'end': word.get('end', 0),
                                'probability': word.get('probability', 0.0)
                            }
                            segment_data['words'].append(word_data)

                    segments.append(segment_data)

            logger.info(f"Successfully transcribed {audio_file.filename}")

            return jsonify({
                'transcript': transcript,
                'model': model_name,
                'language': detected_language,
                'filename': audio_file.filename,
                'segments': segments,
                'success': True
            })
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except OSError:
                pass
    
    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        return jsonify({
            'error': str(e),
            'success': False
        }), 500

@app.route('/clear-cache', methods=['POST'])
def clear_model_cache():
    """Clear the model cache to free memory."""
    global model_cache
    cleared_models = list(model_cache.keys())
    model_cache.clear()
    logger.info(f"Cleared model cache: {cleared_models}")
    return jsonify({
        'message': 'Model cache cleared',
        'cleared_models': cleared_models
    })

def check_whisper_installation():
    """Check if Whisper is properly installed."""
    try:
        import whisper
        logger.info("Whisper is properly installed")
        return True
    except ImportError:
        logger.error("Whisper is not installed. Please run: pip install openai-whisper")
        return False

def main():
    """Main function to start the server."""
    print("=" * 60)
    print("Local Whisper Transcription Server")
    print("=" * 60)

    # Check installation
    if not check_whisper_installation():
        print("\nInstallation Error:")
        print("Whisper is not installed. Please install it with:")
        print("pip install openai-whisper flask flask-cors")
        print("\nAlternatively, if you're running from npm:")
        print("The web app will still work, but transcription features will be disabled.")
        sys.exit(1)

    print("\nWhisper is installed and ready!")
    print(f"Available models: {', '.join(AVAILABLE_MODELS.keys())}")
    print("\nStarting server...")
    print("Server URL: http://localhost:8001")
    print("Health check: http://localhost:8001/health")
    print("Available models: http://localhost:8001/models")
    print("\nTips:")
    print("   - First time using a model will download it automatically")
    print("   - Models are cached in memory for faster subsequent use")
    print("   - Use Ctrl+C to stop the server")
    print("   - Server automatically starts with 'npm run dev'")
    print("=" * 60)

    try:
        # Suppress Flask's default startup messages for cleaner output
        import logging
        log = logging.getLogger('werkzeug')
        log.setLevel(logging.WARNING)

        print("\nServer is ready for transcription requests!")
        print("   Waiting for audio files from the web application...")
        print("   (Server will remain quiet until requests are received)\n")

        app.run(host='localhost', port=8001, debug=False)
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"\nPort 8001 is already in use!")
            print("   - Another Whisper server might be running")
            print("   - Try stopping other instances or change the port")
            print("   - Check with: netstat -an | grep 8001")
        else:
            print(f"\nServer error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected server error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
