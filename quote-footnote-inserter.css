/* Quote and Footnote Inserter Styles */

.quote-footnote-inserter-view {
    padding: 20px;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.quote-footnote-layout {
    display: flex;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

.quote-footnote-controls-panel {
    width: 300px;
    flex-shrink: 0;
    background: var(--bg-panel);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
}

.quote-footnote-main-panel {
    flex: 1;
    display: flex;
    gap: 20px;
    min-width: 0;
}

.quote-footnote-control-group {
    margin-bottom: 25px;
}

.quote-footnote-control-group h3 {
    margin: 0 0 15px 0;
    color: var(--text-panel-heading);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 5px;
}

.quote-footnote-text-area-wrapper {
    flex: 2;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.quote-footnote-text-area-wrapper textarea {
    width: 100%;
    height: 100%;
    min-height: 400px;
    padding: 15px;
    border: 1px solid var(--border-input);
    border-radius: 8px;
    background: var(--bg-input);
    color: var(--text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    outline: none;
}

.quote-footnote-text-area-wrapper textarea:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.quote-footnote-annotations-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 250px;
}

.quote-footnote-annotations-wrapper h3 {
    margin: 0 0 15px 0;
    color: var(--text-panel-heading);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 5px;
}

.annotations-list {
    flex: 1;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-panel);
    overflow-y: auto;
    padding: 10px;
}

.annotation-item {
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-input);
    cursor: pointer;
    transition: all 0.2s ease;
}

.annotation-item:hover {
    background: var(--bg-list-item-hover);
    border-color: var(--accent-primary);
}

.annotation-item.selected {
    background: var(--bg-list-item-selected);
    color: var(--text-list-item-selected);
    border-color: var(--bg-list-item-selected);
}

.annotation-item .annotation-type {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 5px;
    opacity: 0.8;
}

.annotation-item .annotation-text {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.annotation-item.selected .annotation-type,
.annotation-item.selected .annotation-text {
    color: var(--text-list-item-selected);
}

/* Status bar for Quote and Footnote Inserter */
.quote-footnote-inserter-view .status-bar {
    margin-top: 10px;
    padding: 8px 15px;
    background: var(--bg-status-bar);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 12px;
    color: var(--text-status-bar);
}

/* Responsive design */
@media (max-width: 1200px) {
    .quote-footnote-layout {
        flex-direction: column;
    }
    
    .quote-footnote-controls-panel {
        width: 100%;
        max-height: 200px;
    }
    
    .quote-footnote-main-panel {
        flex-direction: column;
    }
    
    .quote-footnote-annotations-wrapper {
        min-width: 100%;
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .quote-footnote-inserter-view {
        padding: 10px;
    }
    
    .quote-footnote-layout {
        gap: 10px;
    }
    
    .quote-footnote-controls-panel {
        padding: 15px;
    }
    
    .quote-footnote-text-area-wrapper textarea {
        min-height: 300px;
        padding: 10px;
        font-size: 13px;
    }
}

/* Highlighted text in textarea */
.quote-footnote-text-area-wrapper textarea.highlighted {
    background: linear-gradient(
        to right,
        var(--bg-input) 0%,
        rgba(52, 152, 219, 0.1) 50%,
        var(--bg-input) 100%
    );
}

/* Loading state */
.quote-footnote-inserter-view .loading {
    opacity: 0.6;
    pointer-events: none;
}

.quote-footnote-inserter-view .loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error states */
.quote-footnote-inserter-view .error {
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    border-color: #ff6b6b;
}

.quote-footnote-inserter-view .success {
    color: #51cf66;
    background: rgba(81, 207, 102, 0.1);
    border-color: #51cf66;
}
