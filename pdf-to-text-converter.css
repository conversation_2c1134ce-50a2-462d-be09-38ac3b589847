/* PDF to Text Converter Styles */

.pdf-to-text-converter-view {
    padding: 20px;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.pdf-to-text-layout {
    display: flex;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

.pdf-to-text-controls-panel {
    width: 350px;
    flex-shrink: 0;
    background: var(--bg-panel);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
}

.pdf-to-text-main-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 0;
}

.pdf-to-text-control-group {
    margin-bottom: 25px;
}

.pdf-to-text-control-group h3 {
    margin: 0 0 15px 0;
    color: var(--text-panel-heading);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 5px;
}

.config-item {
    margin-bottom: 15px;
}

.config-item label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-weight: 500;
}

.config-item input[type="text"],
.config-item input[type="password"],
.config-item select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-input);
    border-radius: 4px;
    background: var(--bg-input);
    color: var(--text-primary);
    font-size: 14px;
    box-sizing: border-box;
}

.config-item input[type="text"]:focus,
.config-item input[type="password"]:focus,
.config-item select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.config-item input[type="checkbox"] {
    margin-right: 8px;
}

.file-details {
    margin-top: 10px;
    padding: 10px;
    background: var(--bg-input);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.pdf-preview-wrapper,
.conversion-progress-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.pdf-preview-wrapper h3,
.conversion-progress-wrapper h3 {
    margin: 0 0 15px 0;
    color: var(--text-panel-heading);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 5px;
}

.pdf-preview {
    flex: 1;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-input);
    overflow: auto;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    min-height: 200px;
}

.pdf-preview canvas {
    max-width: 100%;
    max-height: 100%;
    border: 1px solid var(--border-secondary);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.progress-bar-container {
    margin-bottom: 15px;
    padding: 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar::after {
    content: '';
    display: block;
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-bar.active::after {
    width: var(--progress-width, 0%);
}

#progress-text {
    text-align: center;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.conversion-log {
    flex: 1;
    background: var(--bg-input);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 15px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-primary);
    min-height: 200px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-entry.info {
    color: var(--text-primary);
}

.log-entry.success {
    color: var(--status-ok-color);
}

.log-entry.warning {
    color: var(--status-flagged-color);
}

.log-entry.error {
    color: var(--status-error-color);
}

.log-entry .timestamp {
    color: var(--text-secondary);
    font-size: 11px;
}

/* Status bar for PDF to Text Converter */
.pdf-to-text-converter-view .status-bar {
    margin-top: 10px;
    padding: 8px 15px;
    background: var(--bg-status-bar);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 12px;
    color: var(--text-status-bar);
}

/* Button states */
.control-button:disabled {
    background: var(--button-disabled-bg);
    color: var(--text-secondary);
    cursor: not-allowed;
    opacity: 0.6;
}

.control-button.processing {
    position: relative;
    overflow: hidden;
}

.control-button.processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive design */
@media (max-width: 1200px) {
    .pdf-to-text-layout {
        flex-direction: column;
    }
    
    .pdf-to-text-controls-panel {
        width: 100%;
        max-height: 300px;
    }
    
    .pdf-to-text-main-panel {
        flex-direction: row;
    }
}

@media (max-width: 768px) {
    .pdf-to-text-converter-view {
        padding: 10px;
    }
    
    .pdf-to-text-layout {
        gap: 10px;
    }
    
    .pdf-to-text-controls-panel {
        padding: 15px;
        width: 300px;
    }
    
    .pdf-to-text-main-panel {
        flex-direction: column;
    }
    
    .config-item input[type="text"],
    .config-item input[type="password"],
    .config-item select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Loading states */
.pdf-to-text-converter-view .loading {
    opacity: 0.6;
    pointer-events: none;
}

.pdf-to-text-converter-view .loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Connection status indicators */
.connection-status {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.connection-status.connected {
    background: var(--status-ok-color);
}

.connection-status.disconnected {
    background: var(--status-error-color);
}

.connection-status.testing {
    background: var(--status-processing-color);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
