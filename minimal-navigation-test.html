<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .app-header {
            background: #2c3e50;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .header-nav-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .header-nav-button:hover {
            background: #2980b9;
        }
        .header-nav-button.active-nav-button {
            background: #e74c3c;
        }
        .view {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 300px;
        }
        .hidden-view {
            display: none;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Minimal Navigation Test</h1>
    <p>This tests the exact same navigation structure as the main app.</p>
    
    <div class="app-header">
        <button id="audiobook-verification-btn" class="header-nav-button">Audiobook Verification</button>
        <button id="audiobook-text-editor-btn" class="header-nav-button">Audiobook Text Editor</button>
        <button id="ai-voice-creator-btn" class="header-nav-button">AI Voice Creator</button>
        <button id="quote-footnote-inserter-btn" class="header-nav-button">Quote and Footnote Inserter</button>
        <button id="pdf-to-text-converter-btn" class="header-nav-button">PDF to Text Converter</button>
    </div>
    
    <div id="app-container" class="view">
        <h2>Audiobook Verification View</h2>
        <p>This is the main verification view content.</p>
        <p>If you can see this and the navigation works, then the issue is elsewhere.</p>
    </div>
    
    <div id="text-editor-view-container" class="view hidden-view">
        <h2>Audiobook Text Editor View</h2>
        <p>This is the text editor view content.</p>
        <p>Navigation is working if you can see this when clicking the Text Editor tab.</p>
    </div>
    
    <div id="ai-voice-creator-view-container" class="view hidden-view">
        <h2>AI Voice Creator View</h2>
        <p>This is the AI voice creator view content.</p>
        <p>Navigation is working if you can see this when clicking the AI Voice Creator tab.</p>
    </div>
    
    <div id="quote-footnote-inserter-view-container" class="view hidden-view">
        <h2>Quote and Footnote Inserter View</h2>
        <p>This is the quote and footnote inserter view content.</p>
        <p>Navigation is working if you can see this when clicking the Quote and Footnote Inserter tab.</p>
    </div>
    
    <div id="pdf-to-text-converter-view-container" class="view hidden-view">
        <h2>PDF to Text Converter View</h2>
        <p>This is the PDF to text converter view content.</p>
        <p>Navigation is working if you can see this when clicking the PDF to Text Converter tab.</p>
    </div>
    
    <div id="debug-log" class="debug-log">
        Debug log will appear here...
    </div>

    <script>
        // Replicate the exact navigation logic from domElements.js
        const debugLog = document.getElementById('debug-log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[NAV TEST] ${message}`);
        }
        
        // Get DOM elements
        const audiobookVerificationBtn = document.getElementById('audiobook-verification-btn');
        const audiobookTextEditorBtn = document.getElementById('audiobook-text-editor-btn');
        const aiVoiceCreatorBtn = document.getElementById('ai-voice-creator-btn');
        const quoteFootnoteInserterBtn = document.getElementById('quote-footnote-inserter-btn');
        const pdfToTextConverterBtn = document.getElementById('pdf-to-text-converter-btn');
        
        const appContainer = document.getElementById('app-container');
        const textEditorViewContainer = document.getElementById('text-editor-view-container');
        const aiVoiceCreatorViewContainer = document.getElementById('ai-voice-creator-view-container');
        const quoteFootnoteInserterViewContainer = document.getElementById('quote-footnote-inserter-view-container');
        const pdfToTextConverterViewContainer = document.getElementById('pdf-to-text-converter-view-container');
        
        log('DOM elements initialized');
        
        // Check if all elements exist
        const elements = {
            audiobookVerificationBtn,
            audiobookTextEditorBtn,
            aiVoiceCreatorBtn,
            quoteFootnoteInserterBtn,
            pdfToTextConverterBtn,
            appContainer,
            textEditorViewContainer,
            aiVoiceCreatorViewContainer,
            quoteFootnoteInserterViewContainer,
            pdfToTextConverterViewContainer
        };
        
        for (const [name, element] of Object.entries(elements)) {
            if (element) {
                log(`✅ ${name} found`);
            } else {
                log(`❌ ${name} missing`);
            }
        }
        
        // Navigation arrays
        const navButtons = [audiobookVerificationBtn, audiobookTextEditorBtn, aiVoiceCreatorBtn, quoteFootnoteInserterBtn, pdfToTextConverterBtn];
        const views = [appContainer, textEditorViewContainer, aiVoiceCreatorViewContainer, quoteFootnoteInserterViewContainer, pdfToTextConverterViewContainer];
        
        // Switch view function (exact copy from domElements.js)
        const switchView = (viewToShow, buttonToActivate) => {
            log(`switchView called: ${viewToShow?.id}, ${buttonToActivate?.id}`);
            
            // Hide all views by adding the 'hidden-view' class
            views.forEach(view => {
                if (view) {
                    view.classList.add('hidden-view');
                    log(`Hidden view: ${view.id}`);
                }
            });
            
            // Show the target view by removing the class
            if (viewToShow) {
                viewToShow.classList.remove('hidden-view');
                log(`Showed view: ${viewToShow.id}`);
            }
            
            // Update the active state on the navigation buttons
            navButtons.forEach(button => {
                if (button) {
                    button.classList.remove('active-nav-button');
                    log(`Deactivated button: ${button.id}`);
                }
            });
            if (buttonToActivate) {
                buttonToActivate.classList.add('active-nav-button');
                log(`Activated button: ${buttonToActivate.id}`);
            }
        };
        
        // Add event listeners (exact copy from domElements.js)
        log('Adding navigation event listeners...');
        
        if (audiobookVerificationBtn) {
            audiobookVerificationBtn.addEventListener('click', () => {
                log('Verification button clicked!');
                switchView(appContainer, audiobookVerificationBtn);
            });
        }
        
        if (audiobookTextEditorBtn) {
            audiobookTextEditorBtn.addEventListener('click', () => {
                log('Text Editor button clicked!');
                switchView(textEditorViewContainer, audiobookTextEditorBtn);
            });
        }
        
        if (aiVoiceCreatorBtn) {
            aiVoiceCreatorBtn.addEventListener('click', () => {
                log('AI Voice Creator button clicked!');
                switchView(aiVoiceCreatorViewContainer, aiVoiceCreatorBtn);
            });
        }
        
        if (quoteFootnoteInserterBtn) {
            quoteFootnoteInserterBtn.addEventListener('click', () => {
                log('Quote and Footnote Inserter button clicked!');
                switchView(quoteFootnoteInserterViewContainer, quoteFootnoteInserterBtn);
            });
        }
        
        if (pdfToTextConverterBtn) {
            pdfToTextConverterBtn.addEventListener('click', () => {
                log('PDF to Text Converter button clicked!');
                switchView(pdfToTextConverterViewContainer, pdfToTextConverterBtn);
            });
        }
        
        // Set initial view
        log('Setting initial view...');
        switchView(appContainer, audiobookVerificationBtn);
        
        log('Navigation test initialized successfully');
    </script>
</body>
</html>
